# Proposed Canonical UI Package Structure

## Overview
This document outlines a more canonical and scalable structure for the `@nui/ui` package, following modern React component library best practices.

## Proposed Directory Structure

```
packages/ui/
├── package.json
├── tsconfig.json
├── README.md
├── CHANGELOG.md
├── src/
│   ├── index.ts                     # Main entry point
│   ├── styles/
│   │   ├── index.css               # Main stylesheet
│   │   ├── themes.css              # Theme definitions
│   │   ├── components.css          # Component-specific styles
│   │   └── utilities.css           # Utility classes
│   ├── components/
│   │   ├── index.ts                # Re-export all components
│   │   ├── ui/                     # Core UI components
│   │   │   ├── button/
│   │   │   │   ├── index.ts
│   │   │   │   ├── button.tsx
│   │   │   │   ├── button.stories.tsx
│   │   │   │   ├── button.test.tsx
│   │   │   │   └── button.types.ts
│   │   │   ├── input/
│   │   │   │   ├── index.ts
│   │   │   │   ├── input.tsx
│   │   │   │   ├── input.stories.tsx
│   │   │   │   ├── input.test.tsx
│   │   │   │   └── input.types.ts
│   │   │   ├── card/
│   │   │   ├── dialog/
│   │   │   ├── dropdown/
│   │   │   └── ...
│   │   ├── layout/                 # Layout components
│   │   │   ├── container/
│   │   │   ├── grid/
│   │   │   ├── stack/
│   │   │   ├── flex/
│   │   │   └── ...
│   │   ├── navigation/             # Navigation components
│   │   │   ├── breadcrumb/
│   │   │   ├── menu/
│   │   │   ├── tabs/
│   │   │   ├── pagination/
│   │   │   └── ...
│   │   ├── feedback/               # Feedback components
│   │   │   ├── alert/
│   │   │   ├── toast/
│   │   │   ├── loading/
│   │   │   ├── progress/
│   │   │   └── ...
│   │   ├── data-display/           # Data display components
│   │   │   ├── table/
│   │   │   ├── list/
│   │   │   ├── badge/
│   │   │   ├── avatar/
│   │   │   └── ...
│   │   ├── form/                   # Form components
│   │   │   ├── checkbox/
│   │   │   ├── radio/
│   │   │   ├── select/
│   │   │   ├── textarea/
│   │   │   ├── form-field/
│   │   │   └── ...
│   │   └── providers/              # Context providers
│   │       ├── theme-provider/
│   │       ├── toast-provider/
│   │       └── ...
│   ├── hooks/
│   │   ├── index.ts
│   │   ├── use-theme.ts
│   │   ├── use-toast.ts
│   │   ├── use-disclosure.ts
│   │   ├── use-media-query.ts
│   │   └── ...
│   ├── utils/
│   │   ├── index.ts
│   │   ├── cn.ts                   # Class name utility
│   │   ├── theme.ts                # Theme utilities
│   │   ├── colors.ts               # Color utilities
│   │   ├── animation.ts            # Animation utilities
│   │   └── ...
│   ├── lib/
│   │   ├── index.ts
│   │   ├── constants.ts            # Global constants
│   │   ├── storage.ts              # Storage utilities
│   │   ├── validators.ts           # Validation utilities
│   │   └── ...
│   ├── types/
│   │   ├── index.ts
│   │   ├── theme.ts                # Theme-related types
│   │   ├── components.ts           # Component prop types
│   │   ├── utils.ts                # Utility types
│   │   └── ...
│   └── tokens/
│       ├── index.ts
│       ├── colors.ts               # Color tokens
│       ├── spacing.ts              # Spacing tokens
│       ├── typography.ts           # Typography tokens
│       ├── shadows.ts              # Shadow tokens
│       └── ...
├── dist/                           # Build output
└── .storybook/                     # Storybook configuration
```

## Key Improvements

### 1. **Clear Component Categorization**
- **ui/**: Core interactive components (Button, Input, Dialog)
- **layout/**: Layout and positioning components (Container, Grid, Stack)
- **navigation/**: Navigation-specific components (Menu, Breadcrumb, Tabs)
- **feedback/**: User feedback components (Alert, Toast, Loading)
- **data-display/**: Data presentation components (Table, List, Badge)
- **form/**: Form-specific components (Checkbox, Radio, Select)
- **providers/**: Context providers and global state

### 2. **Consistent Component Structure**
Each component follows the same pattern:
```
component-name/
├── index.ts              # Main export
├── component-name.tsx    # Component implementation
├── component-name.types.ts # TypeScript types
├── component-name.stories.tsx # Storybook stories
└── component-name.test.tsx    # Unit tests
```

### 3. **Separated Concerns**
- **styles/**: All CSS and styling concerns
- **hooks/**: Reusable React hooks
- **utils/**: Pure utility functions
- **lib/**: Library-specific utilities and helpers
- **types/**: TypeScript type definitions
- **tokens/**: Design tokens and theme values

### 4. **Design Tokens**
Centralized design tokens for:
- Colors
- Spacing
- Typography
- Shadows
- Animations

### 5. **Better Entry Points**
- Main `index.ts` exports everything needed
- Category-specific exports (e.g., `components/ui/index.ts`)
- Individual component exports for tree-shaking

## Migration Benefits

1. **Scalability**: Easy to add new components in appropriate categories
2. **Maintainability**: Clear separation of concerns and consistent structure
3. **Developer Experience**: Predictable file locations and imports
4. **Tree Shaking**: Optimized bundle sizes with granular exports
5. **Testing**: Co-located tests with components
6. **Documentation**: Co-located stories for better component docs
7. **Type Safety**: Centralized and well-organized type definitions

## Example Imports After Migration

```tsx
// Category-based imports
import { Button, Input, Dialog } from '@nui/ui/components/ui'
import { Container, Grid, Stack } from '@nui/ui/components/layout'
import { Alert, Toast } from '@nui/ui/components/feedback'

// Individual component imports (tree-shaking friendly)
import { Button } from '@nui/ui/components/ui/button'
import { useTheme } from '@nui/ui/hooks'
import { cn } from '@nui/ui/utils'

// Main package import (everything)
import { Button, useTheme, cn } from '@nui/ui'
```

## Implementation Strategy

1. **Phase 1**: Reorganize existing components and remove duplicates
2. **Phase 2**: Implement new directory structure
3. **Phase 3**: Add missing component categories
4. **Phase 4**: Implement design tokens system
5. **Phase 5**: Add comprehensive testing and documentation
