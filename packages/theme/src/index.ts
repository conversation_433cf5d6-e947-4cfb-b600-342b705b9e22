// Components
export { ThemeProvider } from "./components/theme-provider";
export { ThemeToggle } from "./components/theme-toggle";
export type { ThemeProviderProps } from "./components/theme-provider";

// Hooks
export { useTheme, useThemeOptional } from "./components/theme-provider";

// Types
export type {
  ThemeMode,
  ThemeVariant,
  ThemeConfig,
  UseThemeReturn,
} from "./lib/types";

// Utils (for advanced usage)
export { applyTheme, getSystemTheme, isDarkMode } from "./lib/utils";
export { getStoredTheme, persistTheme } from "../../ui/src/lib/storage";
export { THEME_MODES, DEFAULT_CONFIG, DEFAULT_VARIANTS } from "../../ui/src/lib/constants";
