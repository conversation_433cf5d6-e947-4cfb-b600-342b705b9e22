import { useCallback, useEffect, useState } from "react";
import type {
  ThemeConfig,
  ThemeMode,
  ThemeVariant,
  UseThemeReturn,
} from "../types";
import { THEME_MODES } from "../lib/constants";
import { getStoredTheme, persistTheme } from "../lib/storage";
import { isDarkMode, applyTheme } from "../utils/theme";

/**
 * React hook for theme management
 */
export function useTheme(): UseThemeReturn {
  const [config, setConfig] = useState<ThemeConfig>(getStoredTheme);

  // Listen for system theme changes when in auto mode
  useEffect(() => {
    if (config.mode !== "auto") return;

    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    const handleChange = () => applyTheme(config);

    mediaQuery.addEventListener("change", handleChange);
    return () => mediaQuery.removeEventListener("change", handleChange);
  }, [config]);

  // Apply theme whenever config changes
  useEffect(() => {
    applyTheme(config);
    persistTheme(config);
  }, [config]);

  const setMode = useCallback((mode: ThemeMode) => {
    setConfig((prev) => ({ ...prev, mode }));
  }, []);

  const setVariant = useCallback((variant: ThemeVariant) => {
    setConfig((prev) => ({ ...prev, variant }));
  }, []);

  const toggleMode = useCallback(() => {
    setConfig((prev) => {
      const currentIndex = THEME_MODES.indexOf(prev.mode);
      const nextMode = THEME_MODES[(currentIndex + 1) % THEME_MODES.length];
      return { ...prev, mode: nextMode };
    });
  }, []);

  const cycleVariant = useCallback(() => {
    // This would need to be implemented with available variants
    // For now, just a placeholder
    console.warn('cycleVariant not implemented in standalone hook');
  }, []);

  return {
    mode: config.mode,
    variant: config.variant,
    isDark: isDarkMode(config),
    availableVariants: ['default'], // Default fallback
    setMode,
    setVariant,
    toggleMode,
    cycleVariant,
  };
}
