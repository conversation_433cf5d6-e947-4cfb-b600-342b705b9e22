// Components
export { ThemeProvider } from "./components/theme-provider";
export { ThemeToggle } from "./components/theme-toggle";
export type { ThemeProviderProps } from "./components/theme-provider";

// Hooks
export { useTheme, useThemeOptional } from "./components/theme-provider";

// Types
export type {
  ThemeMode,
  ThemeVariant,
  ThemeConfig,
  UseThemeReturn,
} from "./types";

// Utils (for advanced usage)
export { applyTheme, getSystemTheme, isDarkMode } from "./utils/theme";
export { getStoredTheme, persistTheme } from "./lib/storage";
export { THEME_MODES, DEFAULT_CONFIG, DEFAULT_VARIANTS } from "./lib/constants";

export * from "./components/button";

