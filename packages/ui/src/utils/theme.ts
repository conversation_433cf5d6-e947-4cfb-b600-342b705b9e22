import type { ThemeConfig } from "../types";

/**
 * Get system color scheme preference
 */
export function getSystemTheme(): "light" | "dark" {
  if (typeof window === "undefined") return "light";
  return window.matchMedia("(prefers-color-scheme: dark)").matches
    ? "dark"
    : "light";
}

/**
 * Determine if current config should use dark mode
 */
export function isDarkMode(config: ThemeConfig): boolean {
  return config.mode === "auto"
    ? getSystemTheme() === "dark"
    : config.mode === "dark";
}

/**
 * Apply theme classes to document element
 */
export function applyTheme(config: ThemeConfig): void {
  if (typeof document === "undefined") return;

  const { classList } = document.documentElement;

  // Remove existing theme classes
  classList.remove("light", "dark");
  Array.from(classList)
    .filter((cls) => cls.startsWith("theme-"))
    .forEach((cls) => classList.remove(cls));

  // Apply new theme classes
  const resolvedMode = config.mode === "auto" ? getSystemTheme() : config.mode;
  classList.add(resolvedMode);

  if (config.variant !== "default") {
    classList.add(`theme-${config.variant}`);
  }
}
