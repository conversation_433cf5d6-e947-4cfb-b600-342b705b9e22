# UI Package Migration Plan

## Current Issues to Address

### 1. Duplicate Components
- **Button component exists in 3 locations:**
  - `src/components/button.tsx`
  - `src/components/ui/button.tsx`
  - `src/components/ui/button/button.tsx`

### 2. Inconsistent Utility Organization
- `src/utils/classes.ts` (contains `cn` function)
- `src/utils/theme.ts` (theme utilities)
- `src/lib/constants.ts` (constants)
- `src/lib/storage.ts` (storage utilities)

### 3. Mixed Component Organization
- Theme components mixed with UI components
- No clear categorization system

## Step-by-Step Migration Plan

### Phase 1: Clean Up Duplicates and Consolidate

#### Step 1.1: Remove Duplicate Button Components
```bash
# Keep the most complete version and remove others
# Analysis shows all three are nearly identical
# Recommend keeping: src/components/ui/button/button.tsx (most organized)
```

#### Step 1.2: Consolidate Utilities
```bash
# Move all utilities to organized structure:
# src/utils/cn.ts (from classes.ts)
# src/utils/theme.ts (keep as is)
# src/lib/constants.ts (keep as is)
# src/lib/storage.ts (keep as is)
```

### Phase 2: Implement New Directory Structure

#### Step 2.1: Create New Directory Structure
```bash
mkdir -p src/components/{ui,layout,navigation,feedback,data-display,form,providers}
mkdir -p src/{hooks,utils,lib,types,tokens,styles}
```

#### Step 2.2: Move Existing Components
```bash
# UI Components
src/components/ui/button/ (consolidate from duplicates)

# Providers
src/components/providers/theme-provider/ (from src/components/theme-provider.tsx)

# Theme Toggle (decide category - could be UI or providers)
src/components/ui/theme-toggle/ (from src/components/theme-toggle.tsx)
```

#### Step 2.3: Reorganize Styles
```bash
# Move and organize CSS
src/styles/index.css (main entry)
src/styles/themes.css (from src/theme.css)
src/styles/components.css (component-specific styles)
src/styles/utilities.css (utility classes)
```

### Phase 3: Update Import/Export Structure

#### Step 3.1: Create Category Index Files
```typescript
// src/components/ui/index.ts
export * from './button'
// ... other UI components

// src/components/providers/index.ts
export * from './theme-provider'
// ... other providers

// src/components/index.ts
export * from './ui'
export * from './providers'
export * from './layout'
// ... other categories
```

#### Step 3.2: Update Main Index File
```typescript
// src/index.ts
// Components (organized by category)
export * from './components/ui'
export * from './components/providers'

// Hooks
export * from './hooks'

// Utils (for advanced usage)
export * from './utils'
export * from './lib'

// Types
export * from './types'
```

### Phase 4: Implement Design Tokens

#### Step 4.1: Create Token Files
```typescript
// src/tokens/colors.ts
export const colors = {
  background: 'oklch(0.985 0 0)',
  foreground: 'oklch(0.145 0 0)',
  // ... other colors
}

// src/tokens/spacing.ts
export const spacing = {
  xs: '0.25rem',
  sm: '0.5rem',
  // ... other spacing values
}
```

#### Step 4.2: Update Theme System
```typescript
// src/utils/theme.ts
import { colors } from '../tokens/colors'
// Use tokens instead of hardcoded values
```

### Phase 5: Add Missing Infrastructure

#### Step 5.1: Component Template
```typescript
// Template for new components
// src/components/ui/[component]/index.ts
export { Component } from './component'
export type { ComponentProps } from './component.types'

// src/components/ui/[component]/component.tsx
// Component implementation

// src/components/ui/[component]/component.types.ts
// TypeScript types

// src/components/ui/[component]/component.stories.tsx
// Storybook stories (future)

// src/components/ui/[component]/component.test.tsx
// Unit tests (future)
```

## File-by-File Migration Map

### Current → New Location

```
src/components/button.tsx → DELETE (duplicate)
src/components/ui/button.tsx → DELETE (duplicate)
src/components/ui/button/button.tsx → src/components/ui/button/button.tsx ✓
src/components/ui/button/index.ts → src/components/ui/button/index.ts ✓

src/components/theme-provider.tsx → src/components/providers/theme-provider/theme-provider.tsx
src/components/theme-toggle.tsx → src/components/ui/theme-toggle/theme-toggle.tsx

src/utils/classes.ts → src/utils/cn.ts
src/utils/theme.ts → src/utils/theme.ts ✓
src/lib/constants.ts → src/lib/constants.ts ✓
src/lib/storage.ts → src/lib/storage.ts ✓
src/types/index.ts → src/types/theme.ts (rename for clarity)

src/theme.css → src/styles/themes.css
```

## Updated Package.json Scripts

```json
{
  "scripts": {
    "build": "tsup --config ../../.config/tsup.config.ts",
    "dev": "tsup --config ../../.config/tsup.config.ts --watch",
    "typecheck": "tsc --noEmit",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "storybook": "storybook dev -p 6006",
    "build-storybook": "storybook build",
    "prepack": "bun run build && clean-package",
    "postpack": "clean-package restore"
  }
}
```

## Benefits After Migration

1. **No more duplicate components**
2. **Clear component categorization**
3. **Consistent file structure**
4. **Better tree-shaking support**
5. **Easier to find and maintain components**
6. **Scalable for future growth**
7. **Better developer experience**

## Risk Mitigation

1. **Backup current structure** before starting migration
2. **Update imports gradually** to avoid breaking changes
3. **Test thoroughly** after each phase
4. **Update documentation** to reflect new structure
5. **Communicate changes** to team members

## Timeline Estimate

- **Phase 1**: 1-2 hours (cleanup duplicates)
- **Phase 2**: 2-3 hours (restructure directories)
- **Phase 3**: 1-2 hours (update imports/exports)
- **Phase 4**: 2-3 hours (implement tokens)
- **Phase 5**: 1-2 hours (add infrastructure)

**Total**: 7-12 hours for complete migration
